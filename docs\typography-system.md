# 标准化博客排版系统

## 概述

这是一个基于现代设计原则的标准化博客排版系统，提供了优秀的阅读体验和多语言支持。该系统使用CSS自定义属性（CSS Variables）实现，具有高度的可定制性和性能优化。

## 核心特性

### 1. 设计令牌系统
- 使用CSS自定义属性统一管理所有设计变量
- 支持浅色/深色主题的无缝切换
- 响应式设计令牌，在不同断点下自动调整

### 2. 字体系统
- **正文**：衬线字体(Merriweather, Georgia)，提供更好的阅读体验
- **标题**：无衬线字体(system-ui, Roboto)，确保现代感和清晰度
- **代码**：等宽字体(SF Mono, Menlo, Monaco)，保证代码对齐
- **多语言优化**：针对中日韩等语言的专门字体栈

### 3. 响应式字号
- **移动端(≤600px)**：基础字号16px，标题范围20px-39px
- **平板端(601-900px)**：基础字号17px，标题范围22px-42px
- **桌面端(≥901px)**：基础字号18px，标题范围25px-49px

### 4. 科学的行高设置
- **正文内容**：1.6倍行高，确保最佳阅读体验
- **标题文字**：1.3倍行高，保持紧凑美观
- **CJK语言**：1.8倍行高，适应汉字特点

## 文件结构

```
src/
├── styles/
│   ├── globals.css              # 全局样式，导入排版系统
│   └── blog-typography.css      # 标准化排版系统核心文件
├── components/
│   └── blog/
│       └── MarkdownContent.tsx  # 支持标准化排版的组件
└── app/
    └── [locale]/
        └── typography-demo/     # 排版系统演示页面
            └── page.tsx
```

## 使用方法

### 1. 在组件中使用

```tsx
import { MarkdownContent } from '@/components/blog/MarkdownContent';

// 使用标准化排版系统
<MarkdownContent 
  content={markdownContent} 
  useStandardTypography={true}
/>

// 使用原有的Tailwind样式
<MarkdownContent 
  content={markdownContent} 
  useStandardTypography={false}
/>
```

### 2. 直接使用CSS类

```html
<article class="blog-content blog-typography">
  <h1>文章标题</h1>
  <p>文章内容...</p>
</article>
```

### 3. 自定义样式

```css
/* 覆盖默认的设计令牌 */
:root {
  --font-size-base: 1.125rem; /* 自定义基础字号 */
  --line-height-base: 1.7;    /* 自定义行高 */
  --content-max-width: 75ch;  /* 自定义内容宽度 */
}
```

## 设计令牌参考

### 颜色系统
```css
/* 浅色主题 */
--color-background: #f8f9fa;
--color-surface: #ffffff;
--color-text-primary: #212529;
--color-text-secondary: #6c757d;
--color-accent-primary: #0d6efd;
--color-border: #dee2e6;

/* 深色主题（自动检测） */
--color-background: #121212;
--color-surface: #1e1e1e;
--color-text-primary: rgba(255, 255, 255, 0.87);
--color-text-secondary: rgba(255, 255, 255, 0.60);
--color-accent-primary: #79a6f8;
--color-border: rgba(255, 255, 255, 0.12);
```

### 字体系统
```css
--font-family-sans: system-ui, -apple-system, "Segoe UI", Roboto, sans-serif;
--font-family-serif: "Merriweather", "Georgia", serif;
--font-family-mono: "SF Mono", "Menlo", "Monaco", "Consolas", monospace;
```

### 排版标尺
```css
--font-size-base: 1rem;      /* 16px */
--font-size-h1: 2.441rem;    /* 移动端 */
--font-size-h2: 1.953rem;
--font-size-h3: 1.563rem;
--line-height-base: 1.6;
--line-height-heading: 1.3;
```

### 布局与间距
```css
--spacing-unit: 1rem;
--content-max-width: 70ch;
--border-radius: 0.375rem;
```

## 多语言支持

系统自动为不同语言应用优化的字体栈：

```css
/* 中文简体 */
html:lang(zh-CN) { 
  --font-family-base: 'PingFang SC', 'Noto Sans SC', 'Microsoft YaHei', sans-serif; 
  --line-height-base: 1.8; 
}

/* 中文繁体 */
html:lang(zh-TW), html:lang(zh-HK) { 
  --font-family-base: 'PingFang TC', 'Noto Sans TC', 'Heiti TC', sans-serif; 
  --line-height-base: 1.8; 
}

/* 日语 */
html:lang(ja) { 
  --font-family-base: 'Hiragino Sans', 'Noto Sans JP', 'Yu Gothic UI', sans-serif; 
  --line-height-base: 1.8; 
}

/* 韩语 */
html:lang(ko) { 
  --font-family-base: 'Apple SD Gothic Neo', 'Noto Sans KR', 'Nanum Gothic', sans-serif; 
  --line-height-base: 1.8; 
}
```

## 性能优化

1. **系统字体优先**：使用系统字体避免网络请求
2. **零布局偏移**：避免字体加载导致的布局跳动
3. **CSS变量**：减少重复代码，提升维护性
4. **渐进增强**：基础样式确保所有设备可读

## 可访问性

- 符合WCAG 2.1 AA标准的颜色对比度
- 支持用户浏览器字体大小设置
- 渐进增强设计，确保基础功能在所有设备上可用

## 演示页面

访问 `/typography-demo` 页面查看排版系统的实际效果和对比。

## 迁移指南

### 从现有Tailwind样式迁移

1. 将 `useStandardTypography={true}` 添加到 `MarkdownContent` 组件
2. 移除自定义的Tailwind排版类
3. 根据需要调整设计令牌

### 自定义主题

1. 在你的CSS文件中覆盖相应的CSS变量
2. 确保同时定义浅色和深色主题的变量
3. 测试在不同屏幕尺寸下的效果

## 最佳实践

1. **内容优先**：始终以内容的可读性为首要考虑
2. **一致性**：在整个应用中保持排版的一致性
3. **测试**：在不同设备和语言环境下测试效果
4. **渐进增强**：确保基础功能在所有环境下都能正常工作

## 技术支持

如有问题或建议，请参考项目的主要规范文档或联系开发团队。
