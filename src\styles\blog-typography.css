/* ==========================================================================
   现代博客的基石设计系统
   版本: 1.0
   描述: 一个基于行业最佳实践的、支持主题化、响应式和多语言的CSS框架。
   ========================================================================== */

/* ==========================================================================
   1. 全局设计令牌 (CSS 自定义属性) - 默认浅色主题
   ========================================================================== */
:root {
  /* 1.1 颜色系统 */
  --color-background: #f8f9fa;
  --color-surface: #ffffff;
  --color-text-primary: #212529;
  --color-text-secondary: #6c757d;
  --color-accent-primary: #0d6efd;
  --color-accent-primary-hover: #0b5ed7;
  --color-border: #dee2e6;
  --color-code-background: #e9ecef;
  --color-code-text: #212529;

  /* 1.2 字体系统 */
  --font-family-sans: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif;
  --font-family-serif: "Merriweather", "Georgia", serif;
  --font-family-mono: "SF Mono", "Menlo", "Monaco", "Consolas", "Liberation Mono", "Courier New", monospace;
  
  /* 默认字体栈 */
  --font-family-base: var(--font-family-sans);

  /* 1.3 排版标尺 */
  --font-size-base: 1rem; /* 16px */
  --font-size-small: 0.875rem; /* 14px */
  --font-size-h1: 2.441rem;
  --font-size-h2: 1.953rem;
  --font-size-h3: 1.563rem;
  --font-size-h4: 1.25rem;
  --font-size-h5: 1.125rem;
  --font-size-h6: 1rem;
  
  --line-height-base: 1.6;
  --line-height-heading: 1.3;

  /* 1.4 布局与间距 */
  --spacing-unit: 1rem;
  --content-max-width: 70ch;
  --border-radius: 0.375rem;
}

/* ==========================================================================
   2. 深色模式覆盖
   ========================================================================== */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: #121212;
    --color-surface: #1e1e1e;
    --color-text-primary: rgba(255, 255, 255, 0.87);
    --color-text-secondary: rgba(255, 255, 255, 0.60);
    --color-accent-primary: #79a6f8;
    --color-accent-primary-hover: #a3c3ff;
    --color-border: rgba(255, 255, 255, 0.12);
    --color-code-background: rgba(255, 255, 255, 0.08);
    --color-code-text: rgba(255, 255, 255, 0.87);
  }
}

/* ==========================================================================
   3. 语言特定覆盖
   ========================================================================== */
html:lang(zh-CN) { --font-family-base: 'PingFang SC', 'Noto Sans SC', 'Microsoft YaHei', sans-serif; --line-height-base: 1.8; }
html:lang(zh-TW), html:lang(zh-HK) { --font-family-base: 'PingFang TC', 'Noto Sans TC', 'Heiti TC', sans-serif; --line-height-base: 1.8; }
html:lang(ja) { --font-family-base: 'Hiragino Sans', 'Noto Sans JP', 'Yu Gothic UI', sans-serif; --line-height-base: 1.8; }
html:lang(ko) { --font-family-base: 'Apple SD Gothic Neo', 'Noto Sans KR', 'Nanum Gothic', sans-serif; --line-height-base: 1.8; }

/* ==========================================================================
   4. 基础与重置样式
   ========================================================================== */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 100%; /* 16px */
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==========================================================================
   5. 布局样式
   ========================================================================== */
.blog-content {
  width: 100%;
  max-width: var(--content-max-width);
  margin-left: auto;
  margin-right: auto;
  padding: calc(var(--spacing-unit) * 2) var(--spacing-unit);
}

/* ==========================================================================
   6. 排版元素样式
   ========================================================================== */
.blog-typography h1, 
.blog-typography h2, 
.blog-typography h3, 
.blog-typography h4, 
.blog-typography h5, 
.blog-typography h6 {
  margin-top: calc(var(--spacing-unit) * 2);
  margin-bottom: var(--spacing-unit);
  font-family: var(--font-family-sans); /* 标题使用非衬线体 */
  font-weight: 700;
  line-height: var(--line-height-heading);
}

.blog-typography h1 { font-size: var(--font-size-h1); }
.blog-typography h2 { font-size: var(--font-size-h2); }
.blog-typography h3 { font-size: var(--font-size-h3); }
.blog-typography h4 { font-size: var(--font-size-h4); }
.blog-typography h5 { font-size: var(--font-size-h5); }
.blog-typography h6 { font-size: var(--font-size-h6); }

.blog-typography p {
  margin-top: 0;
  margin-bottom: var(--spacing-unit);
  font-family: var(--font-family-serif); /* 正文使用衬线体 */
}

.blog-typography a {
  color: var(--color-accent-primary);
  text-decoration: underline;
  transition: color 0.15s ease-in-out;
}

.blog-typography a:hover {
  color: var(--color-accent-primary-hover);
}

.blog-typography ul, 
.blog-typography ol {
  padding-left: calc(var(--spacing-unit) * 1.5);
}

.blog-typography blockquote {
  margin: 0 0 var(--spacing-unit);
  padding: var(--spacing-unit) calc(var(--spacing-unit) * 1.5);
  border-left: 4px solid var(--color-border);
  background-color: var(--color-surface);
  color: var(--color-text-secondary);
}

.blog-typography code, 
.blog-typography pre {
  font-family: var(--font-family-mono);
}

.blog-typography code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: var(--color-code-background);
  color: var(--color-code-text);
  border-radius: var(--border-radius);
}

.blog-typography pre {
  display: block;
  padding: var(--spacing-unit);
  margin: 0 0 var(--spacing-unit);
  font-size: 85%;
  line-height: 1.45;
  word-break: break-all;
  word-wrap: break-word;
  color: var(--color-code-text);
  background-color: var(--color-code-background);
  border-radius: var(--border-radius);
  overflow-x: auto;
}

.blog-typography pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  background-color: transparent;
  border-radius: 0;
}

.blog-typography hr {
  border: 0;
  border-top: 1px solid var(--color-border);
  margin: calc(var(--spacing-unit) * 2) 0;
}

/* ==========================================================================
   7. 响应式断点
   ========================================================================== */
/* 中等屏幕 (Tablet) */
@media (min-width: 601px) {
  :root {
    --font-size-base: 1.0625rem; /* 17px */
  }
}

/* 大屏幕 (Desktop) */
@media (min-width: 901px) {
  :root {
    --font-size-base: 1.125rem; /* 18px */
    --font-size-h1: 3.052rem;
    --font-size-h2: 2.441rem;
    --font-size-h3: 1.953rem;
    --font-size-h4: 1.563rem;
  }
}
