import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { MarkdownContent } from '@/components/blog/MarkdownContent';

export const metadata: Metadata = {
  title: '排版系统演示 - Typography Demo',
  description: '展示标准化博客排版系统的效果',
};

const demoContent = `
# 标准化博客排版系统演示

这是一个展示我们标准化博客排版系统的演示页面。该系统基于现代设计原则，提供了优秀的阅读体验。

## 设计原则

我们的排版系统遵循以下核心原则：

### 1. 可读性优先

正文使用衬线字体，提供更好的阅读体验。行高设置为1.6倍，确保文字之间有足够的空间，减少阅读疲劳。

### 2. 响应式设计

字体大小会根据屏幕尺寸自动调整：
- 移动端：基础字号16px
- 平板端：基础字号17px  
- 桌面端：基础字号18px

### 3. 多语言支持

系统针对不同语言进行了优化：

- **中文简体**：使用PingFang SC, Noto Sans SC字体栈
- **中文繁体**：使用PingFang TC, Noto Sans TC字体栈
- **日语**：使用Hiragino Sans, Noto Sans JP字体栈
- **韩语**：使用Apple SD Gothic Neo, Noto Sans KR字体栈

## 排版元素展示

### 段落文本

这是一个标准的段落文本示例。我们使用衬线字体来提供更好的阅读体验，同时保持合适的行高和字间距。

这是另一个段落，用来展示段落之间的间距效果。良好的垂直韵律是优秀排版的关键。

### 列表展示

无序列表示例：
- 第一个列表项
- 第二个列表项，内容稍长一些，用来展示列表项的换行效果
- 第三个列表项

有序列表示例：
1. 首先，确定设计目标
2. 然后，选择合适的字体
3. 最后，调整间距和布局

### 引用块

> 这是一个引用块的示例。引用块使用了不同的背景色和左边框来区分普通文本，同时使用次要文字颜色来降低视觉权重。

### 代码展示

行内代码示例：\`console.log('Hello World')\`

代码块示例：

\`\`\`javascript
function greet(name) {
  return \`Hello, \${name}!\`;
}

console.log(greet('World'));
\`\`\`

### 链接样式

这是一个[链接示例](https://example.com)，展示了链接的样式和悬停效果。

---

## 主题支持

系统支持浅色和深色两种主题，会根据用户的系统偏好自动切换。

### 浅色主题（默认）
- 背景色：#f8f9fa (浅灰背景) + #ffffff (内容背景)
- 文字色：#212529 (主要文字) + #6c757d (次要文字)
- 强调色：#0d6efd (链接) + #0b5ed7 (悬停)

### 深色主题（自动检测）
- 背景色：#121212 (深色背景) + #1e1e1e (内容背景)
- 文字色：rgba(255,255,255,0.87) (主要) + rgba(255,255,255,0.60) (次要)
- 强调色：#79a6f8 (链接) + #a3c3ff (悬停)

## 技术实现

该排版系统使用CSS自定义属性（CSS Variables）实现，具有以下优势：

1. **统一管理**：所有设计令牌集中管理
2. **主题切换**：通过媒体查询自动检测用户偏好
3. **语言适配**：使用:lang()选择器提供语言特定样式
4. **性能优化**：使用系统字体，避免网络请求

这个系统为我们的博客内容提供了一致、优雅且高性能的排版体验。
`;

interface TypographyDemoPageProps {
  params: {
    locale: string;
  };
}

export default async function TypographyDemoPage({ params }: TypographyDemoPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'common' });

  return (
    <div className="min-h-screen bg-background">
      {/* 页面头部 */}
      <div className="bg-gradient-to-r from-mystical-50 to-gold-50 dark:from-dark-900 dark:to-dark-800 py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-mystical-900 dark:text-white mb-4">
            排版系统演示
          </h1>
          <p className="text-xl text-mystical-700 dark:text-mystical-300 max-w-2xl mx-auto">
            展示标准化博客排版系统的设计原则和视觉效果
          </p>
        </div>
      </div>

      {/* 演示内容 */}
      <div className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 切换按钮 */}
          <div className="mb-8 p-4 bg-card rounded-lg border border-border">
            <p className="text-sm text-foreground-secondary mb-2">
              排版样式对比：
            </p>
            <div className="flex gap-4">
              <span className="text-sm">
                <strong>当前显示：</strong>标准化CSS排版系统
              </span>
            </div>
          </div>

          {/* 标准化排版演示 */}
          <div className="bg-card rounded-lg border border-border p-8">
            <MarkdownContent 
              content={demoContent} 
              useStandardTypography={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
